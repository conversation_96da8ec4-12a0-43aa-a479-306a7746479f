const utils = require('./utils/util');
const user = require('./request/user')

App({
    onLaunch() {
        //检查登陆状态

    },

    globalData: {
        // 现场信息反馈功能的基础URL - 根据实际IIS部署地址调整
        feedbackBaseUrl: 'http://192.168.16.94:8080'
    },

    /**
     * 现场信息反馈功能的网络请求方法
     * 用于fbindex页面及相关功能的API调用
     */
    request(options) {
        return new Promise((resolve, reject) => {
            // 从车辆维保小程序的存储中获取用户信息
            const userInfo = wx.getStorageSync("userInfo");
            const currentCompany = wx.getStorageSync("currentCompany");

            // 默认请求配置
            const defaultOptions = {
                method: 'GET',
                timeout: 10000,
                header: {
                    "Content-Type": "application/json",
                },
            };

            // 如果有用户信息，添加到header中
            if (userInfo) {
                // 添加用户信息到header（保留原有格式）
                defaultOptions.header["X-User-Info"] = JSON.stringify({
                    PersonId: userInfo.PersonId || userInfo.userId,
                    PersonName: userInfo.PersonName,
                    Phone: userInfo.Phone
                });

                // 添加认证token（符合后端认证中间件的要求）
                const userId = userInfo.PersonId || userInfo.userId;
                const username = userInfo.PersonName || 'user';
                const timestamp = Date.now();
                const tokenData = `${userId}:${username}:${timestamp}`;
                const token = Buffer.from(tokenData).toString('base64');
                defaultOptions.header["authorization"] = token;
            }

            // 如果有当前公司信息，添加到header中
            if (currentCompany) {
                try {
                    const companyJson = JSON.stringify(currentCompany);
                    defaultOptions.header["X-Current-Company"] = encodeURIComponent(companyJson);
                } catch (error) {
                    console.warn('序列化公司信息失败:', error);
                }
            }

            // 合并默认配置和传入的配置
            const requestOptions = {
                ...defaultOptions,
                ...options,
                header: {
                    ...defaultOptions.header,
                    ...options.header,
                },
                // 处理URL：如果是完整URL则直接使用，否则拼接baseUrl
                url: options.url.startsWith("http")
                    ? options.url
                    : `${this.globalData.feedbackBaseUrl}${options.url}`,
                success: (res) => {
                    resolve(res);
                },
                fail: (error) => {
                    console.error('请求失败:', error);
                    reject(error);
                },
            };

            wx.request(requestOptions);
        });
    }
})
