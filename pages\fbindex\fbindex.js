// fbindex.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    currentCompany: null,
    companies: [],
    stats: {
      totalProjects: 0,
      totalTasks: 0,
      totalFeedbacks: 0,
    },
    recentProjects: [],
    showCompanyModal: false,
    loading: false,




    // 搜索相关
    searchKeyword: "",
    showSearchBar: true,
  },

  onLoad() {
    this.checkLogin();
  },

  onShow() {
    if (this.data.userInfo) {
      this.refreshUserInfo(); // 刷新用户信息和公司列表
      // 只在首次加载或没有工程数据时才加载数据
      if (this.data.recentProjects.length === 0) {
        this.loadData();
      } else {
        // 只刷新统计数据，不重新加载工程列表（暂时禁用）
        // this.loadStats();
        console.log("页面显示，跳过数据刷新");
      }
    }
  },

  // 检查登录状态
  checkLogin() {
    // 读取车辆维保小程序存储的用户信息
    const userInfo = wx.getStorageSync("userInfo");

    console.log("检查登录状态 - userInfo:", userInfo);
    console.log("userInfo类型:", typeof userInfo);
    console.log("userInfo.PersonId:", userInfo ? userInfo.PersonId : "userInfo为空");

    // 检查用户信息是否存在以及是否有有效的用户ID
    const hasValidUserId = userInfo && (userInfo.PersonId || userInfo.userId);

    if (!hasValidUserId) {
      wx.showModal({
        title: '提示',
        content: '请先在车辆维保模块登录',
        showCancel: false,
        success: function(res) {
          if (res.confirm) {
            // 跳转回车辆维保小程序首页
            wx.navigateBack({
              delta: 1
            });
          }
        }
      });
      return;
    }

    // 从车辆维保小程序的用户信息中提取需要的字段
    const fbUserInfo = {
      PersonId: userInfo.PersonId || userInfo.userId,
      PersonName: userInfo.PersonName || userInfo.name || userInfo.userName,
      Phone: userInfo.Phone,
      Company: userInfo.Company || []
    };

    // 确保PersonId字段存在
    if (!fbUserInfo.PersonId) {
      console.error("无法获取有效的用户ID");
      wx.showToast({
        title: '用户信息异常',
        icon: 'none'
      });
      return;
    }

    console.log("现场信息反馈用户信息:", fbUserInfo);

    // 处理公司信息
    const companies = userInfo.Company || [];
    let currentCompany = null;

    // 找到当前选中的公司（IsMain为true的公司）
    if (companies.length > 0) {
      currentCompany = companies.find(company => company.IsMain) || companies[0];

      // 转换公司信息格式以适配现场信息反馈功能
      currentCompany = {
        id: currentCompany.CompanyId,
        name: currentCompany.CompanyName,
        ...currentCompany
      };
    }

    this.setData({
      userInfo: fbUserInfo,
      companies: companies,
      currentCompany: currentCompany,
    });

    console.log("设置的公司信息:", currentCompany);

    this.loadData();
  },

  // 刷新用户信息和公司列表
  async refreshUserInfo() {
    try {
      // 直接从本地存储读取用户信息，不调用API
      const userInfo = wx.getStorageSync("userInfo");

      if (userInfo) {
        console.log("从本地存储刷新用户信息:", userInfo);

        // 处理公司信息
        const companies = userInfo.Company || [];
        let currentCompany = null;

        // 找到当前选中的公司（IsMain为true的公司）
        if (companies.length > 0) {
          currentCompany = companies.find(company => company.IsMain) || companies[0];

          // 转换公司信息格式以适配现场信息反馈功能
          currentCompany = {
            id: currentCompany.CompanyId,
            name: currentCompany.CompanyName,
            ...currentCompany
          };
        }

        this.setData({
          userInfo: {
            PersonId: userInfo.PersonId || userInfo.userId,
            PersonName: userInfo.PersonName,
            Phone: userInfo.Phone,
            Company: companies
          },
          companies: companies,
          currentCompany: currentCompany,
        });

        return;
      }

      // 如果本地存储中没有用户信息，显示错误
      throw new Error("本地存储中没有用户信息");

    } catch (error) {
      console.error("刷新用户信息失败:", error.message || error.errMsg);
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
    }
  },

  // 切换公司（通用方法）
  switchCompany(company) {
    try {
      // 直接在本地切换公司，不调用API
      wx.setStorageSync("currentCompany", company);
      this.setData({
        currentCompany: company,
      });

      console.log("已切换到公司:", company.name);

      // 重新加载数据
      this.loadData();

    } catch (error) {
      console.error("切换公司失败:", error);
      wx.showToast({
        title: '切换公司失败',
        icon: 'none'
      });
    }
  },

  // 加载数据
  async loadData() {
    if (!this.data.currentCompany) {
      return;
    }

    this.setData({
      loading: true,
      recentProjects: [],
      // 重置搜索关键词但保持搜索栏显示
      searchKeyword: ""
    });

    try {
      // 加载统计数据
      await this.loadStats();
      // 加载最近工程
      await this.loadProjects();

    } catch (error) {
      console.error("加载数据失败:", error.message || error.errMsg);
      // 设置默认数据以防API调用失败
      this.setData({
        stats: {
          totalProjects: 0,
          totalTasks: 0
        },
        recentProjects: []
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      // 获取项目统计
      const projectRes = await app.request({
        url: "/api/projects",
        method: "GET"
      });





      if (projectRes.data.success) {
        const projects = projectRes.data.data;
        let stats = {
          totalProjects: projects.length,
          totalTasks: projects.reduce((sum, p) => sum + (p.task_count || 0), 0),
        };



        this.setData({ stats });
      }
    } catch (error) {
      console.error("加载统计数据失败:", error);
    }
  },

  // 加载当前公司的工程列表
  async loadProjects() {
    if (!this.data.currentCompany) {
      this.setData({
        recentProjects: []
      });
      return;
    }

    try {
      const { searchKeyword } = this.data;

      const requestData = {
        keyword: searchKeyword,
      };

      const res = await app.request({
        url: "/api/projects",
        method: "GET",
        data: requestData,
      });

      if (res.data && res.data.success) {
        const projects = res.data.data || [];

        // 为每个工程添加格式化的显示信息
        const formattedProjects = projects.map((project) => ({
          ...project,
          // 格式化任务数量显示
          taskCountText:
            project.task_count > 0
              ? `${project.task_count}个任务单`
              : "暂无任务",
          // 格式化创建时间显示
          createdTimeText: this.formatDate(project.created_at),
        }));

        this.setData({
          recentProjects: formattedProjects
        });
      } else {
        console.error("获取工程列表失败:", res.data?.message || "未知错误");
        this.setData({ recentProjects: [] });

        // 显示错误提示
        if (res.data && res.data.message) {
          wx.showToast({
            title: res.data.message,
            icon: "none",
            duration: 2000,
          });
        }
      }
    } catch (error) {
      console.error("加载工程列表网络错误:", error.message || error.errMsg);
      this.setData({ recentProjects: [] });

      // 显示网络错误提示
      wx.showToast({
        title: "网络连接失败",
        icon: "none",
        duration: 2000,
      });
    }
  },



  // 格式化日期显示
  formatDate(dateString) {
    if (!dateString) return "";

    const date = new Date(dateString);
    const now = new Date();
    const diffTime = now - date;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return "今天";
    } else if (diffDays === 1) {
      return "昨天";
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString("zh-CN", {
        month: "short",
        day: "numeric",
      });
    }
  },

  // 显示公司选择弹窗
  async onSelectCompany() {
    // 先刷新公司列表，确保显示最新的公司信息
    await this.refreshUserInfo();
    this.setData({ showCompanyModal: true });
  },

  // 关闭公司选择弹窗
  onCloseCompanyModal(e) {
    // 兼容modal组件的事件参数
    this.setData({ showCompanyModal: false });
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 选择公司
  async onSelectCompanyItem(e) {
    const company = e.currentTarget.dataset.company;

    try {
      const res = await app.request({
        url: "/api/auth/switch-company",
        method: "POST",
        data: { companyId: company.id },
      });

      if (res.data && res.data.success) {
        wx.setStorageSync("currentCompany", company);
        this.setData({
          currentCompany: company,
          showCompanyModal: false,
        });
        this.loadData();
        wx.showToast({
          title: "切换成功",
          icon: "success",
        });
      } else {
        wx.showToast({
          title: (res.data && res.data.message) || "切换失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("切换公司失败:", error);
      wx.showToast({
        title: "网络错误",
        icon: "none",
      });
    }
  },



  // 点击工程项
  onProjectTap(e) {
    const project = e.currentTarget.dataset.project;
    wx.navigateTo({
      url: `/subpackages/task-management/task-list/task-list?projectId=${project.id}`,
    });
  },


  // 点击项目统计
  onProjectsTap() {
    if (!this.data.currentCompany) {
      wx.showToast({
        title: "请先选择公司",
        icon: "none",
      });
      return;
    }

    wx.navigateTo({
      url: "/subpackages/project-management/project-list/project-list",
    });
  },

  // 查看全部工程
  onViewAllProjects() {
    if (!this.data.currentCompany) {
      wx.showToast({
        title: "请先选择公司",
        icon: "none",
      });
      return;
    }

    wx.navigateTo({
      url: "/subpackages/project-management/project-list/project-list",
    });
  },







  // 下拉刷新
  async onPullDownRefresh() {
    try {
      console.log("首页下拉刷新开始");

      // 检查登录状态
      const userInfo = wx.getStorageSync("userInfo");
      const hasValidUserId = userInfo && (userInfo.PersonId || userInfo.userId);

      if (!hasValidUserId) {
        console.log("用户未登录，返回首页");
        wx.navigateBack({
          delta: 1
        });
        return;
      }

      // 刷新用户信息和公司列表
      await this.refreshUserInfo();

      // 重新加载所有数据
      await this.loadData();

      console.log("首页下拉刷新完成");

      wx.showToast({
        title: "刷新成功",
        icon: "success",
        duration: 1500,
      });
    } catch (error) {
      console.error("首页下拉刷新失败:", error);
      wx.showToast({
        title: "刷新失败，请检查网络连接",
        icon: "none",
        duration: 2000,
      });
    } finally {
      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
    }
  },



  // 搜索相关方法

  // 搜索输入 - 实时搜索
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword,
    });

    // 实时搜索
    this.loadProjects();
    console.log("搜索关键词:", keyword);
  },

  // 返回车辆维保首页
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },
});