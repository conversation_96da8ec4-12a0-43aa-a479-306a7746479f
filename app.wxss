/**app.wxss**/
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 通用按钮样式 */
.btn {
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  margin: 10rpx 0;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.btn:active {
  transform: scale(0.95);
}

.btn-primary {
  background-color: #1296DB;
  color: white;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1rpx solid #ddd;
}

.btn-danger {
  background-color: #f44336;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

